﻿using GamesEngine.Business.Liquidity.Sentinels.Outbound;
using System;
using System.Collections.Generic;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;

namespace GamesEngine.Business.Liquidity.Sentinels
{
    internal abstract class EgressSentinel : Sentinel
    {
        internal EgressSentinel() { }

        internal IEnumerable<SentinelTask> OutboundTasks => sentinelTasks.Tasks;

        internal SentinelTask FindOutboundTask(object objectKey)
        {
            if (objectKey == null) throw new ArgumentNullException(nameof(objectKey));
            return sentinelTasks.FindTask(objectKey);
        }

        internal ConfimationsOutboundTask AwaitWithdrawalConfirmations(bool itIsThePresent, DateTime now, DispenserReady dispenserReady)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));

            var confimationsOutboundTask = sentinelTasks.AwaitWithdrawalConfirmations(dispenserReady);
            confimationsOutboundTask.StartOutboundTask(itIsThePresent);
            return confimationsOutboundTask;
        }
    }
}
