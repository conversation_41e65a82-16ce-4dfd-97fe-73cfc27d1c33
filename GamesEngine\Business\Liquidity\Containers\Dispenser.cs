﻿using GamesEngine.Business.Liquidity.Sentinels.Outbound;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Domains;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal abstract class Dispenser : Container
    {
        internal int Id { get; private set; }

        internal string Name { get; private set; }

        internal string Description { get; private set; }

        internal bool Enabled { get; private set; } = true;

        internal DateTime StartDate { get; private set; }

        internal Liquid Liquid { get; private set; }

        protected List<Withdrawal> _withdrawals;
        internal IEnumerable<Withdrawal> Withdrawals => _withdrawals;

        internal Dispenser(int id, string name, string description, Liquid liquid, string kind, decimal amount, DateTime startDate, List<Withdrawal> _withdrawals) : base(kind)
        {
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (liquid == null) throw new ArgumentNullException(nameof(liquid));
            if (amount < 0) throw new GameEngineException("Amount cannot be negative.");
            if (startDate == DateTime.MinValue) throw new ArgumentNullException(nameof(startDate));
            if (_withdrawals == null) throw new ArgumentNullException(nameof(_withdrawals));

            Id = id;
            Name = name;
            Description = description;
            Liquid = liquid;
            Amount = amount;
            StartDate = startDate;
            this._withdrawals = _withdrawals;
        }

        internal string Type => this.GetType().Name;

        internal void MoveWithdrawal(int dispenserId, Withdrawal withdrawal)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
            if (dispenserId <= 0) throw new ArgumentNullException(nameof(dispenserId));
            
            if (_withdrawals.Contains(withdrawal))
            {
                _withdrawals.Remove(withdrawal);
                Dispenser dispenser = Liquid.Outlet.FindDispenser(dispenserId);
                dispenser._withdrawals.Add(withdrawal);
            }
            else
            {
                throw new GameEngineException("Withdrawal not found in this dispenser.");
            }
        }


        internal class DispenserInbox : Dispenser
        {
            internal DispenserInbox(string kind, Liquid liquid) : base(int.MaxValue, "Inbox", "Inbox for incoming transactions", liquid, kind, 0, DateTime.MaxValue, new List<Withdrawal>())
            {
            }

            internal Withdrawal CreateWithdrawal(DateTime now, int withdrawalId, string pullPaymentId, int authorization, decimal amount, string destionation, string atAddress, Domain domain, int storeId, string externalReference)
            {
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
                if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));
                if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));
                if (authorization <= 0) throw new ArgumentNullException(nameof(authorization));
                if (amount <= 0) throw new ArgumentNullException(nameof(amount));
                if (string.IsNullOrWhiteSpace(destionation)) throw new ArgumentNullException(nameof(destionation));
                if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
                if (domain == null) throw new ArgumentNullException(nameof(domain));
                if (storeId == 0) throw new ArgumentNullException(nameof(storeId)); 
                if (string.IsNullOrWhiteSpace(externalReference)) throw new ArgumentNullException(nameof(externalReference));

                var withdrawal = new Withdrawal(withdrawalId, pullPaymentId, authorization, amount, now, destionation, Id, atAddress, domain, storeId, externalReference);
                _withdrawals.Add(withdrawal);
                return withdrawal;
            }

            internal void CancelWithdrawal(DateTime now, int withdrawalId)
            {
                Withdrawal withdrawal = _withdrawals.FirstOrDefault(w => w.Id == withdrawalId);
                if (withdrawal == null) throw new GameEngineException($"Withdrawal with ID {withdrawalId} not found in the inbox.");
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
                withdrawal.SetNotes($"Withdrawal cancelled on {now}.");
            }

            internal IEnumerable<Withdrawal> WithdrawalsBy(DateTime startDate, DateTime endDate)
            {
                if (startDate == DateTime.MinValue) throw new ArgumentNullException(nameof(startDate));
                if (endDate == DateTime.MinValue) throw new ArgumentNullException(nameof(endDate));
                if (endDate < startDate) throw new ArgumentException("End date cannot be earlier than start date.", nameof(endDate));

                var result = _withdrawals
                    .Where(w => w.CreatedDate >= startDate && w.CreatedDate <= endDate)
                    .ToList();
                return result;
            }
        }

        internal class DispenserReady : Dispenser
        {
            internal decimal AvailableAmount { get; private set; }

            internal bool HasBeenCommitted => outboundTask != null;

            internal DispenserReady(int id, string name, string description, Liquid liquid, string kind, decimal amount, DateTime startDate) : base(id, name, description, liquid, kind, amount, startDate, new List<Withdrawal>())
            {
                AvailableAmount = amount;
            }

            internal DispenserDiscarded Discard(DateTime discardDate)
            {
                if (discardDate == DateTime.MinValue) throw new ArgumentNullException(nameof(discardDate));
                var dispenserDiscarded = new DispenserDiscarded(this, discardDate);
                Liquid.Outlet.AddOrUpdateDispenser(dispenserDiscarded);
                return dispenserDiscarded;
            }

            internal decimal EstimateWithdrawalFee(decimal amount, TimeSpan allowedTime)
            {
                throw new NotImplementedException("This method should be implemented in the derived class.");
            }

            private OutboundTask outboundTask;
            internal void CommitWithdrawals(bool itIsThePresent, DateTime now)
            {
                if (outboundTask != null) throw new InvalidOperationException("There is already an outbound task in progress.");
                if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));

                outboundTask = Liquid.EgressSentinel.AwaitWithdrawalConfirmations(itIsThePresent, now, this);
            }

            public class DispatchedDispenserMessage : LiquidityEventMessage
            {
                internal string Kind { get; private set; }
                internal int DispenserId { get; private set; }

                internal DateTime DispatchedDate { get; private set; }

                internal DispatchedDispenserMessage(string kind, int dispenserId, DateTime dispatchedDate) : base(LiquidityEventType.DispatchedDispenser)
                {
                    if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
                    if (dispenserId <= 0) throw new ArgumentNullException(nameof(dispenserId));
                    if (dispatchedDate == DateTime.MinValue) throw new ArgumentNullException(nameof(dispatchedDate));
                    Kind = kind;
                    DispenserId = dispenserId;
                    DispatchedDate = dispatchedDate;
                }

                public DispatchedDispenserMessage(string message) : base(message)
                {
                }

                protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
                {
                    base.Deserialize(serializedMessage, out fieldOrder);
                    Kind = serializedMessage[fieldOrder++];
                    DispenserId = int.Parse(serializedMessage[fieldOrder++]);
                }

                protected override void InternalSerialize()
                {
                    base.InternalSerialize();
                    AddProperty(Kind).
                    AddProperty(DispenserId);
                }
            }
        }

        internal class DispenserDiscarded : Dispenser
        {
            private Dispenser lastDispenser;

            internal DateTime DiscardDate { get; private set; }

            internal DispenserDiscarded(DispenserReady dispenserReady, DateTime discardDate) : base(dispenserReady.Id, dispenserReady.Name, dispenserReady.Description, dispenserReady.Liquid, dispenserReady.Kind, dispenserReady.Amount, dispenserReady.StartDate, dispenserReady._withdrawals)
            {
                if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));
                if (discardDate == DateTime.MinValue) throw new ArgumentException("Discard date cannot be empty.", nameof(discardDate));
                this.lastDispenser = dispenserReady;
                DiscardDate = discardDate;
            }
        }
    }

}
