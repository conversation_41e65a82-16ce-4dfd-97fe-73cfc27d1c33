﻿using GamesEngine.Business.Liquidity.Sentinels.Inbound;
using GamesEngine.Business.Liquidity.Sentinels.Outbound;
using GamesEngine.Business.Liquidity.Transactions;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;
using static GamesEngine.Business.Liquidity.Containers.Tanker;


namespace GamesEngine.Business.Liquidity.Sentinels
{
    internal class SentinelTasks
    {
        internal static int DELAY_SECONDS_PER_CONFIRMATION = 3;

        private readonly ConcurrentDictionary<object, SentinelTask> inoutboundTasks = new();

        internal SentinelTasks() { }

        internal IEnumerable<SentinelTask> Tasks => inoutboundTasks.Values;

        internal SentinelTask FindTask(object objectKey)
        {
            if (objectKey == null) throw new ArgumentNullException(nameof(objectKey));
            if (inoutboundTasks.TryGetValue(objectKey, out var inboundTask))
            {
                return inboundTask;
            }
            throw new GameEngineException($"No inbound task found for invoice ID: {objectKey}.");
        }

        internal ConfirmationsInboundTask AddConfirmationsWatcher(Deposit deposit, int totalConfirmations)
        {
            if (deposit == null) throw new ArgumentNullException(nameof(deposit), "Deposit cannot be null.");
            if (totalConfirmations < 0) throw new ArgumentNullException(nameof(totalConfirmations), "Total confirmations must be greater than or equal to zero.");

            if (!inoutboundTasks.TryGetValue(deposit.InvoiceId, out SentinelTask confirmsInboundTask))
            {
                confirmsInboundTask = new ConfirmationsInboundTask(this, deposit, totalConfirmations);
                inoutboundTasks.TryAdd(deposit.InvoiceId, confirmsInboundTask);
            }
            return confirmsInboundTask as ConfirmationsInboundTask;
        }

        internal void Detach(SentinelTask inoutboundTask)
        {
            if (inoutboundTask is ConfirmationsInboundTask confirmationsInbound)
            {
                inoutboundTasks.TryRemove(confirmationsInbound.Deposit, out _);
            }
            else if (inoutboundTask is TankerInboundTask tankerInbound)
            {
                inoutboundTasks.TryRemove(tankerInbound.Tanker, out _);
            }
            else if (inoutboundTask is ConfimationsOutboundTask confimationsOutbound)
            {
                inoutboundTasks.TryRemove(confimationsOutbound, out _);
            }
            else
            {
                throw new GameEngineException("Invalid inbound task type. Only ConfirmationsInboundTask can be detached.");
            }
        }

        internal TankerInboundTask AwaitForTanker(TankerSealed tanker)
        {
            if (tanker == null) throw new ArgumentNullException(nameof(tanker));

            if (inoutboundTasks.TryGetValue(tanker, out SentinelTask existingTask))
            {
                return existingTask as TankerInboundTask;
            }

            var newTankerInboundTask = new TankerInboundTask(tanker);
            inoutboundTasks.TryAdd(tanker, newTankerInboundTask);
            return newTankerInboundTask;
        }

        internal ConfimationsOutboundTask AwaitWithdrawalConfirmations(DispenserReady dispenserReady)
        {
            if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));

            var confimationsOutboundTask = new ConfimationsOutboundTask(this, dispenserReady);
            inoutboundTasks.TryAdd(dispenserReady, confimationsOutboundTask);
            return confimationsOutboundTask;
        }
    }
}
