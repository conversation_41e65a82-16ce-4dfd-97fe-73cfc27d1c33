﻿using GamesEngine.Business.Liquidity.Sentinels.Currency;
using GamesEngine.Business.Liquidity.Sentinels.Inbound;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using static GamesEngine.Settings.PaymentManager;

namespace GamesEngine.Business.Liquidity.Sentinels
{
    internal abstract class IngressSentinel : Sentinel
    {
        private readonly ConcurrentDictionary<int, Deposit> wholeDeposits = new ConcurrentDictionary<int, Deposit>();
        private readonly ConcurrentDictionary<string, Deposit> confirmedInvoiceDeposits = new ConcurrentDictionary<string, Deposit>();

        internal IngressSentinel() { }

        internal IEnumerable<SentinelTask> InboundTasks => sentinelTasks.Tasks;

        internal IEnumerable<Deposit> Deposits => wholeDeposits.Values;

        internal SentinelTask FindInboundTask(object objectKey)
        {
            if (objectKey == null) throw new ArgumentNullException(nameof(objectKey));
            return sentinelTasks.FindTask(objectKey);
        }

        internal void AwaitForTanker(bool itIsThePresent, TankerSealed tanker)
        {
            if (tanker == null) throw new ArgumentNullException(nameof(tanker));
            if (tanker.Deposits == null || !tanker.Deposits.Any()) throw new GameEngineException("Tanker must have at least one deposit.");
            try
            {
                TankerInboundTask tankerInboundTask = sentinelTasks.AwaitForTanker(tanker);
                tankerInboundTask.StartInboundTask(itIsThePresent);
            }
            catch
            {
                Loggers.GetIntance().Sentinel.Error($"Error adding confirmation watchers for tanker {tanker.Id}. It may not be being watched by the sentinel.", null);
            }
        }

        internal void UndisburdenTanker(TankerSealed tankerSealed)
        {
            if (tankerSealed == null) throw new ArgumentNullException(nameof(tankerSealed), "Tanker cannot be null.");
            try
            {
                TankerInboundTask inboundTask = sentinelTasks.FindTask(tankerSealed) as TankerInboundTask;
                if (inboundTask == null) throw new GameEngineException($"No inbound task found for tanker {tankerSealed.Id}.");
                inboundTask.Undisburden();
            }
            catch (Exception e)
            {
                Loggers.GetIntance().Sentinel.Error($"Error undisburdening tanker {tankerSealed.Id}: {e.Message}", e);
            }
        }

        internal void AwaitForDeposit(Deposit unconfirmedDeposit, int totalConfirmations)
        {
            if (unconfirmedDeposit == null) throw new ArgumentNullException(nameof(unconfirmedDeposit), "Pending deposit cannot be null.");
            if (totalConfirmations < 0) throw new ArgumentOutOfRangeException(nameof(totalConfirmations), "Total confirmations cannot be negative.");

            try
            {
                wholeDeposits.TryAdd(unconfirmedDeposit.Id, unconfirmedDeposit);
                sentinelTasks.AddConfirmationsWatcher(unconfirmedDeposit, totalConfirmations);
            }
            catch
            {
                Loggers.GetIntance().Sentinel.Error($"Error adding confirmation watcher for deposit {unconfirmedDeposit.Id}. It may not be being watched by the sentinel.", null);
            }
        }

        internal void StopAndDetachInboundTask(Deposit deposit)
        {
            if (deposit == null) throw new ArgumentNullException(nameof(deposit), "Deposit cannot be null.");
            try
            {
                SentinelTask inboundTask = sentinelTasks.FindTask(deposit.InvoiceId);
                switch (inboundTask)
                {
                    case ConfirmationsInboundTask confirmationsInboundTask:
                        confirmationsInboundTask.Cancellation.Cancel();
                        confirmationsInboundTask.StartInboundTask(false);//WILL END IT IF IT WAS CANCELLED
                        break;
                    default:
                        throw new GameEngineException($"Unsupported inbound task type: {inboundTask.GetType().Name} for deposit {deposit.Id}.");
                }
            }
            catch (Exception e)
            {
                Loggers.GetIntance().Sentinel.Error($"No inbound task found for deposit {deposit.Id}.", null);
            }
        }

        internal bool ExistInvoiceInDeposits(string invoiceId)
        {
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId), "Invoice ID cannot be null or empty.");
            return wholeDeposits.Any(d => d.Value.InvoiceId == invoiceId);
        }

        internal bool ExistConfirmedInvoice(string invoiceId)
        {
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId), "Invoice ID cannot be null or empty.");
            return confirmedInvoiceDeposits.ContainsKey(invoiceId);
        }

        internal void InvoiceReceivedPayment(bool itIsThePresent, DateTime now, string invoiceId, decimal invoiceDue, decimal totalPaid, decimal rate, string paymentId)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId));
            if (string.IsNullOrWhiteSpace(paymentId)) throw new ArgumentNullException(nameof(paymentId));
            if (totalPaid <= 0) throw new ArgumentOutOfRangeException(nameof(totalPaid));
            if (rate <= 0) throw new ArgumentOutOfRangeException(nameof(rate), "Rate must be greater than zero.");

            Deposit depositWithInvoice = null;
            foreach (var d in wholeDeposits)
            {
                if (d.Value.InvoiceId == invoiceId)
                {
                    depositWithInvoice = d.Value;
                    break;
                }
            }
            if (depositWithInvoice == null) throw new KeyNotFoundException($"Deposit with invoice ID {invoiceId} not found in the whole deposits.");
            if (!depositWithInvoice.IsPending) return;

            try
            {
                ConfirmationsInboundTask inboundTask = sentinelTasks.FindTask(invoiceId) as ConfirmationsInboundTask;
                if (inboundTask == null) throw new GameEngineException($"No inbound task found for invoice ID: {invoiceId}.");

                inboundTask.StartInboundTask(itIsThePresent, new Invoice(invoiceId, invoiceDue, totalPaid, rate, paymentId));

                if (itIsThePresent)
                {
                    if (Integration.UseKafka)
                    {
                        using var buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}");
                        var invoicePaidMessage = new InvoicePaidMessage(
                        depositWithInvoice.Kind,
                        invoiceId,
                        depositWithInvoice.Destination,
                        depositWithInvoice.ExternalAtAddress,
                        totalPaid,
                        now);
                        buffer.Send(invoicePaidMessage);
                    }
                    _ = DepositInprocessAsync(now, depositWithInvoice);
                }
            }
            catch
            {
                Loggers.GetIntance().Sentinel.Error($"Error confirming invoice {invoiceId}. It may not be being watched by the sentinel.", null);
            }
        }


        private async Task DepositInprocessAsync(DateTime now, Deposit draftConfirmDeposit)
        {
            if (now == DateTime.MinValue) throw new ArgumentException("The provided date is not valid.", nameof(now));
            if (draftConfirmDeposit == null) throw new ArgumentNullException(nameof(draftConfirmDeposit), "Unconfirmed deposit cannot be null.");

            try
            {
                if (!TownSettings.TryToGetActorUrl("LIQUIDITY", out string hostnameIp)) throw new ArgumentException("Hostname or IP address is not configured.", nameof(hostnameIp));

                var client = new HttpClient();
                using var request = new HttpRequestMessage(HttpMethod.Post, $"{hostnameIp}/api/liquidity/{draftConfirmDeposit.Kind}/deposit/{draftConfirmDeposit.Id}/inprocess");

                HttpResponseMessage response = await client.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    Loggers.GetIntance().Sentinel.Debug($"Confirmed deposit {draftConfirmDeposit.Id} at {now} successfully notified.");
                }
                else
                {
                    Loggers.GetIntance().Sentinel.Debug($"Failed to notify confirmed deposit {draftConfirmDeposit.Id} at {now}. Status code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Sentinel.Error($"Error confirming deposit {draftConfirmDeposit.Id} at {now}: {ex.Message}", ex);
            }
        }

        internal static IngressSentinel IngressSentinelByKind(string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
            if (!Enum.TryParse(kind, out Currencies.CODES currencyCode)) throw new ArgumentException($"Invalid currency code: {kind}", nameof(kind));

            IngressSentinel result = null;
            switch (currencyCode)
            {
                case Currencies.CODES.BTC:
                    result = new BTCIngressSentinel();
                    break;
                case Currencies.CODES.ETH:
                    result = new ETHIngressSentinel();
                    break;
                default:
                    throw new ArgumentException($"Unsupported currency code: {kind}", nameof(kind));
            }
            return result;
        }

        internal static EgressSentinel EgressSentinelByKind(string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
            if (!Enum.TryParse(kind, out Currencies.CODES currencyCode)) throw new ArgumentException($"Invalid currency code: {kind}", nameof(kind));

            EgressSentinel result = null;
            switch (currencyCode)
            {
                case Currencies.CODES.BTC:
                    result = new BTCEgressSentinel();
                    break;
                case Currencies.CODES.ETH:
                    result = new ETHEgressSentinel();
                    break;
                default:
                    throw new ArgumentException($"Unsupported currency code: {kind}", nameof(kind));
            }
            return result;
        }


        internal class AccountAmount
        {
            public string Account { get; private set; }
            public decimal Amount { get; private set; }
            public AccountAmount(string account, decimal amount)
            {
                if (string.IsNullOrWhiteSpace(account)) throw new ArgumentNullException(nameof(account), "Account cannot be null or empty.");
                if (amount < 0) throw new ArgumentOutOfRangeException(nameof(amount), "Amount cannot be negative.");
                Account = account;
                Amount = amount;
            }
            public void AppendAmount(decimal amount)
            {
                if (amount < 0) throw new ArgumentOutOfRangeException(nameof(amount), "Amount cannot be negative.");
                Amount += amount;
            }
        }
    }
}
