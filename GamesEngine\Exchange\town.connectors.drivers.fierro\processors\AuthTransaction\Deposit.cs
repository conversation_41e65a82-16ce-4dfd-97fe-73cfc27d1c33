﻿using GamesEngine.Business;
using GamesEngine.Settings;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero.processors.AuthTransaction
{
    internal abstract class Deposit : FierroProcessorDriver
    {
        public static TokenDriver CashierToken { get; set; }
        private RestClient _postClient;

        public string Password { get; set; }
        public string ServicesUrl { get; set; }

        private const float VERSION = 1.1F;
        public override string Description => $"Fiero {nameof(Deposit)} driver {VERSION}";

        public Deposit(string currencyIsoCode)
            : base(town.connectors.drivers.TransactionType.Deposit, VERSION, currencyIsoCode)
        {
        }

        public Deposit(string currencyIsoCode, PaymentMethod paymentMethod)
            : base(town.connectors.drivers.TransactionType.Deposit, VERSION, currencyIsoCode, paymentMethod)
        {
        }
        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            bool passwordChange =
                          Password != CustomSettings.Get(now, "TokenSystemPassword").AsString;
            if (passwordChange)
            {
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsString;
            }
            bool servicesUrlChange =
                         ServicesUrl != CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
            if (servicesUrlChange)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
            }


            if (_postClient == null || _postClient.Options.BaseUrl.ToString() != ServicesUrl)
            {
                _postClient = new RestClient(ServicesUrl);
            }


            DraftDeposit draftConfirmationBody = new DraftDeposit
            {
                ConfirmedAmount = recordSet.Mappings["amount"].AsDecimal,
                ConfirmedCurrency = recordSet.Mappings["currency"].AsString,
                AtAddress = recordSet.Mappings["atAddress"].AsString,
                ExternalReference = int.Parse(recordSet.Mappings["externalReference"].AsString),
                EmployeeName = recordSet.Mappings["employeeName"].AsString
            };

            if (CurrencyIsoCodes.Length > 1) throw new NotImplementedException("Drives must have only one currency code");
            string currencyCode = CurrencyIsoCodes.FirstOrDefault();
            string domain = recordSet.Mappings["domain"].AsString;
            var result = await CreateAuthAsync("/town/v1/transaction/deposit", domain, draftConfirmationBody);
           
            ResponseInvoicePayment auth;

            if (result.AuthorizationId > 0)
            {
                auth = new ResponseInvoicePayment( TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes, result.AuthorizationId, result.DepositId);

                return (T)Convert.ChangeType(auth, typeof(T));
            }
            else
            {
                auth = new ResponseInvoicePayment(TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes, result.AuthorizationId, result.DepositId);
            }
            return (T)Convert.ChangeType(auth, typeof(T));
        }


        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("currency");
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("atAddress");
            CustomSettings.AddVariableParameter("accountNumber");
            CustomSettings.AddVariableParameter("description");
            CustomSettings.AddVariableParameter("entityId");
            CustomSettings.AddVariableParameter("domain");
            CustomSettings.AddVariableParameter("externalReference");
            CustomSettings.AddVariableParameter("employeeName");

            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }

        protected const int MAX_RETRIES = 5;

        private async Task<DepositResponse> CreateAuthAsync(string url,string domain, DraftDeposit transactionBody)
        {
            //int authorization = 0;
            int retryNumber = 0;
            Loggers.GetIntance().Drivers.Debug($"Driver={Id}\nurl={url}");

            var jsonString = JsonConvert.SerializeObject(transactionBody);
            var defaultDepositResponse = new DepositResponse
            {
                AuthorizationId = 0,
                DepositId = 0,
            };

            string responseString = string.Empty;
            while (true)
            {
                try
                {
                    var request = new RestRequest(url,Method.Post);
                   
                    request.AddHeader("apikey", $"{Password.Trim()}");
                    request.AddHeader("domain-url", $"{domain.Trim()}");
                    request.AddHeader("Content-Type", "application/json");
                    request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                    var response = await _postClient.ExecuteAsync(request);

                    responseString = response.Content;
                    Loggers.GetIntance().Drivers.Debug($"Driver={Id}\response={responseString}");

                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        string json = JsonConvert.DeserializeObject<string>(responseString);
                        ResponseAuthorization requesResult = JsonConvert.DeserializeObject<ResponseAuthorization>(json);
                        if (requesResult != null)
                        {
                            DepositResponse deposit = new DepositResponse();
                            deposit.AuthorizationId = requesResult.AuthorizationId;
                            deposit.DepositId = requesResult.DepositId;
                            if (deposit != null)
                            {
                                return deposit;
                            }
                        }
                        return defaultDepositResponse;
                    }
                    else
                    {
                        Loggers.GetIntance().Drivers.Debug($"Driver={Id}\nErrorMessage={response.ErrorMessage}");
                    }
                    break;
                }
                catch (Exception e)
                {
                    retryNumber++;
                    var extraErrorMessage = string.Empty;
                    if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
                    InternalOnError(nameof(CreateAuthAsync), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}");

                    Thread.Sleep(5000);
                    if (retryNumber == MAX_RETRIES)
                    {
                        break;
                    }
                }
            }

            return defaultDepositResponse;
        }

    }


    [DataContract]
    public class ResponseAuthorization
    {
        [DataMember(Name = "depositId")]
        public int DepositId { get; set; }

        [DataMember(Name = "authorizationId")]
        public int AuthorizationId { get; set; }
    }


    public class ResponseInvoicePayment: Result
    {
        public ResponseInvoicePayment(TransactionStatus transactionStatus, string entity, PaymentMethod paymentMethod, string[] currencyIsoCode, int authorizationId, int depositId)
            : base(entity, paymentMethod, currencyIsoCode, TransactionType.Deposit)
        {
            AuthorizationId = authorizationId;
            DepositId = depositId;
        }
       
        public int AuthorizationId { get; }
        public int DepositId { get; }

    }

}
