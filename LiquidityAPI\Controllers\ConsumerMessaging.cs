﻿
using GamesEngine.Business;
using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Custodian;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using town.connectors.drivers;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;
using static GamesEngine.Business.Liquidity.Containers.Dispenser.DispenserReady;
using static GamesEngine.Business.Liquidity.Containers.LiquidityEventMessage;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using static GamesEngine.Business.Liquidity.Containers.Tanker.TankerSealed;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Exchange.town.connectors.drivers.fierro.processors.UnlockDeposit;
using static town.connectors.CustomSettings;

namespace LiquidityAPI.Controllers
{
    internal class ConsumerMessaging
    {
        internal void CreateConsumerForTopics()
        {
            new DispatchTankerConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForLiquidityEvents).StartListening();
        }

        internal class DispatchTankerConsumer : GamesEngine.Settings.Consumer
        {
            internal string Topic { get; private set; }

            internal DispatchTankerConsumer(string group, string topic) : base(group, topic)
            {
                Topic = topic;
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(msg)) throw new ArgumentNullException(nameof(msg));
                    LiquidityEventType messageType = LiquidityEventMessage.GetType(msg);
                    switch (messageType)
                    {
                        case LiquidityEventType.TankerDispatched:
                            var dispatchTankerMessage = new DispatchTankerMessage(msg);
                            if (dispatchTankerMessage.TankerId <= 0) throw new ArgumentNullException(nameof(dispatchTankerMessage.TankerId));

                            var result = LiquidityAPI.Liquidity.PerformChkThenQry(@$"
                            {{
                                existKind = company.System.LiquidFlow.ExistInstance('{dispatchTankerMessage.Kind}');
				                Check(existKind) Error 'The request kind:{dispatchTankerMessage.Kind} does not exist.';

                                if (existKind)
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchTankerMessage.Kind}');
                                    existTanker = liquid.Source.ExistTanker({dispatchTankerMessage.TankerId});
                                    Check(existTanker) Error 'The request tanker id:{dispatchTankerMessage.TankerId} does not exist.';
                                    if (existTanker)
                                    {{
                                        tankerSealed = liquid.Source.FindTanker({dispatchTankerMessage.TankerId});
                                        validSealedType = tankerSealed.Type == '{typeof(TankerSealed).Name}';
                                        Check(validSealedType) Error 'The request tanker id:{dispatchTankerMessage.TankerId} is not a sealed tanker.' + tankerSealed.Type;
                                    }}
                                }}
                            }}
                            ", @$"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchTankerMessage.Kind}');
                                tankerSealed = liquid.Source.FindTanker({dispatchTankerMessage.TankerId});
                        
                                tankerTask = liquid.IngressSentinel.FindInboundTask(tankerSealed);
                                for (tankerTaskDeposits : tankerTask.DepositChainStatuses)
                                {{
                                    tankerDeposit = tankerTaskDeposits;
                                    print tankerDeposit.Id id;
                                    print tankerDeposit.Available available;
                                }}
                            }}
                            ");
                            if (!(result is OkObjectResult)) throw new Exception($"Failed to process tanker with ID {dispatchTankerMessage.TankerId}. Result: {result}");

                            string jsonResult = (result as ObjectResult).Value.ToString();
                            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                            var tankerDepositsDTO = System.Text.Json.JsonSerializer.Deserialize<TankerDepositsDTO>(jsonResult, options);

                            bool tankerCanBeDipatch = tankerDepositsDTO.TankerTaskDeposits.All(d => d.Available == 0);
                            if (tankerCanBeDipatch)
                            {
                                result = LiquidityAPI.Liquidity.PerformCmd(@$"
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchTankerMessage.Kind}');
                                    tankerSealed = liquid.Source.FindTanker({dispatchTankerMessage.TankerId});
                                    tankerSealed.Dispatched(now);
                                }}
                                ");
                                if (!(result is OkObjectResult)) throw new Exception($"Failed to dispatch tanker with ID {dispatchTankerMessage.TankerId}.");
                            }
                            else
                            {
                                var strDepositIds = string.Join(",", tankerDepositsDTO.TankerTaskDeposits.Where(d => d.Available == 0M).Select(d => d.Id));
                                result = LiquidityAPI.Liquidity.PerformCmd(@$"
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchTankerMessage.Kind}');
                                    tankerSealed = liquid.Source.FindTanker({dispatchTankerMessage.TankerId});
                                    Eval('partialTankerId = '+liquid.Source.NextTankerId()+';');
                                    tankerSealed.PartialDispatched(partialTankerId, now, {{{strDepositIds}}});
                                }}
                                ");
                                if (!(result is OkObjectResult)) throw new Exception($"Failed to partially dispatch tanker with ID {dispatchTankerMessage.TankerId}.");
                            }                        
                            break;
                        case LiquidityEventType.DispatchedDispenser:
                            var dispatchedDispenser = new DispatchedDispenserMessage(msg);

                            var resultWithdraws = LiquidityAPI.Liquidity.PerformChkThenQry(@$"
                            {{
                                existKind = company.System.LiquidFlow.ExistInstance('{dispatchedDispenser.Kind}');  
                                Check(existKind) Error 'The request kind:{dispatchedDispenser.Kind} does not exist.';
                                if (existKind)
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchedDispenser.Kind}');
                                    existDispenser = liquid.Outlet.ExistDispenser({dispatchedDispenser.DispenserId});
                                    Check(existDispenser) Error 'The request dispenser id:{dispatchedDispenser.DispenserId} does not exist.';
                                    if (existDispenser)
                                    {{
                                        dispenserReady = liquid.Outlet.FindDispenser({dispatchedDispenser.DispenserId});
                                        validReadyType = dispenserReady.Type == '{typeof(DispenserReady).Name}';
                                        Check(validReadyType) Error 'The request dispenser id:{dispatchedDispenser.DispenserId} is not a ready dispenser.' + dispenserReady.Type;
                                    }}
                                }}
                            }}
                            ", @$"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchedDispenser.Kind}');
                                dispenserReady = liquid.Outlet.FindDispenser({dispatchedDispenser.DispenserId});
                                confirmationsOutboundTask = liquid.EgressSentinel.FindOutboundTask(dispenserReady);
                                for (withdrawals : confirmationsOutboundTask.Withdrawals)
                                {{
                                    withdrawal = withdrawals;
                                    print withdrawal.Amount amount;
                                    print withdrawal.AtAddress atAddress;
                                    print withdrawal.Authorization authorization;
                                    print withdrawal.StoreId storeId;
                                    print withdrawal.Domain.Id domainId;
                                    print withdrawal.Domain.Url domainUrl;
                                    print withdrawal.Domain.AgentId agentId;
                                }}
                            }}
                            ");
                            if (!(resultWithdraws is OkObjectResult)) throw new Exception($"Failed to retrieve withdrawals for dispenser with ID {dispatchedDispenser.DispenserId}. Result: {resultWithdraws}");

                            jsonResult = (resultWithdraws as ObjectResult).Value.ToString();
                            var optionsWithdraws = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                            var dispenserWithdrawals = System.Text.Json.JsonSerializer.Deserialize<DispenserTaskWithdrawals>(jsonResult, optionsWithdraws);
                            if (dispenserWithdrawals == null || dispenserWithdrawals.Withdrawals == null || !dispenserWithdrawals.Withdrawals.Any())
                            {
                                throw new Exception($"No withdrawals found for dispenser with ID {dispatchedDispenser.DispenserId}.");
                            }

                            foreach(var withdrawal in dispenserWithdrawals.Withdrawals)
                            {
                               //driver de UNLOCKDEBIT
                               MultipleProcessors multipleDrivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(withdrawal.AgentId, withdrawal.DomainUrl);
                                var entityId = 4; // fiero
                                PaymentProcessor unlockAndDebitProcessor = multipleDrivers.SearchByX(TransactionType.UnlockDebit, dispatchedDispenser.Kind, PaymentMethod.ThirdParty, entityId);
                                if (unlockAndDebitProcessor == null) throw new Exception($"No unlock and withdrawal processor found for kind: {dispatchedDispenser.Kind} and entityId: {entityId}.");

                                using (RecordSet recordSet = unlockAndDebitProcessor.GetRecordSet())
                                {
                                    recordSet.SetParameter("atAddress", withdrawal.AtAddress);
                                    recordSet.SetParameter("amount", withdrawal.Amount);
                                    recordSet.SetParameter("accountNumber", dispatchedDispenser.Kind);
                                    recordSet.SetParameter("who", $"{this.GetType().Name}");
                                    recordSet.SetParameter("documentNumber", withdrawal.Authorization);
                                    recordSet.SetParameter("storeId", withdrawal.StoreId);
                                    recordSet.SetParameter("concept", $"Withdrawal for dispenser {dispatchedDispenser.DispenserId}");
                                    recordSet.SetParameter("reference", dispatchedDispenser.DispenserId);
                                    recordSet.SetParameter("processorId", unlockAndDebitProcessor.Id);
                                    recordSet.SetParameter("sourceNumber", 17);//Rubicon: DEFAULT SOURCENUMBER
                                    recordSet.SetParameter("sourceName", GamesEngine.Finance.TransactionMessage.NO_SOURCE_NAME);
                                    var driverResult = unlockAndDebitProcessor.Execute<DoneResponse>(DateTime.Now, recordSet);
                                    if (!driverResult.Done)
                                    {
                                        ErrorsSender.Send(new Exception($"Failed to unlock and debit for dispenser with ID {dispatchedDispenser.DispenserId}. Withdrawal: {withdrawal.Amount} at {withdrawal.AtAddress}."), 
                                            $"message {msg}", $"Consumer:{this.GetType().Name}", $"topic:{Topic}");
                                    }
                                }
                            }

                            var resultDispatchedDispenser = LiquidityAPI.Liquidity.PerformCmd(@$"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchedDispenser.Kind}');
                                dispenserReady = liquid.Source.FindDispenser({dispatchedDispenser.DispenserId});
                                dispenserReady.Dispatched(now);
                            }}
                            ");
                            if (!(resultDispatchedDispenser is OkObjectResult)) throw new Exception($"Failed to process dispenser with ID {dispatchedDispenser.DispenserId}. Result: {resultDispatchedDispenser}");
                            break;
                        default:
                            throw new NotSupportedException($"The message type {messageType} is not supported in {this.GetType().Name}.");
                    }
                }
                catch (Exception e)
                {
                    ErrorsSender.Send(e, $"message {msg}", $"Consumer:{this.GetType().Name}", $"topic:{Topic}");
                }
            }
            internal class DispenserTaskWithdrawals
            {
                public List<WithdrawalDTO> Withdrawals { get; set; }
                internal class WithdrawalDTO
                {
                    public decimal Amount { get; set; }
                    public string AtAddress { get; set; }
                    public int Authorization { get; set; }
                    public int StoreId { get; set; }
                    public int DomainId { get; set; }
                    public string DomainUrl { get; set; }
                    public int AgentId { get; set; }
                }
            }

            internal class TankerDepositsDTO
            {
                public List<TankerDeposit> TankerTaskDeposits { get; set; }

                internal class TankerDeposit
                {
                    public int Id { get; set; }
                    public decimal Available { get; set; }
                    public TankerDeposit(int id, decimal available)
                    {
                        Id = id;
                        Available = available;
                    }
                }
            }
        }
    }
}
