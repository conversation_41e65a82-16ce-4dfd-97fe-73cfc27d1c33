﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal abstract class Tank : Container
    {
        protected List<Deposit> deposits;
        private Jar jar;

        internal Tank(int id, string name, string description, DateTime createdAt, Jar jar, string kind, List<Deposit> deposits) : base(kind)
        {
            if (jar == null) throw new GameEngineException("The Jar is null.");
            if (deposits == null) throw new ArgumentNullException(nameof(deposits));
            if (string.IsNullOrEmpty(name)) throw new GameEngineException("The name cannot be null or empty.");
            if (createdAt == DateTime.MinValue) throw new GameEngineException("The createdAt cannot be MinValue.");

            this.Id = id;
            this.Name = name;
            this.Description = description;
            this.jar = jar;
            this.deposits = deposits;
            this.CreatedAt = createdAt;

            foreach (var deposit in this.deposits)
            {
                if (deposit == null) throw new GameEngineException("The Deposit is null.");
                if (deposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");
                Amount += deposit.Amount;
            }

        }

        internal string Type => this.GetType().Name;
        internal Source Source => this.jar.Source;
        internal int Id { get; private set; }
        internal string Name { get; set; }
        internal string Description { get; set; }
        internal int Version => this.jar.Version;
        internal DateTime CreatedAt { get; set; }
        internal IEnumerable<Deposit> Deposits => deposits;

        internal class TankReady : Tank
        {
            internal TankReady(int id, string name, string description, DateTime createdAt, Jar jar, string kind, List<Deposit> deposits) : base(id, name, description, createdAt, jar, kind, deposits) { }

            internal TankReady MergeWith(bool itIsThePresent, int draftTankId, DateTime createdAt, TankReady otherTank)
            {
                if (otherTank == null) throw new ArgumentException("Cannot merge with null tank.");
                if (otherTank == this) throw new ArgumentException("Cannot merge with the same tank.");
                if (otherTank.Deposits.Count() == 0) throw new ArgumentException("Cannot merge with an empty tank.");

                string name = "Merged Tank " + draftTankId;
                string description = $"Merged Tank from {this.Name} and {otherTank.Name}";

                List<Deposit> mergedDeposits = new List<Deposit>(this.Deposits)
                    .Concat(otherTank.Deposits)
                    .Distinct()
                    .ToList();
                var result = new TankReady(draftTankId, name, description, createdAt, this.jar, Kind, mergedDeposits);//Rubicon Todo: Revisar que jar version debe darle origen al merged tank
                if (result.Amount != this.Amount + otherTank.Amount) throw new GameEngineException("The amount of the merged tank is not correct.");
                Source.AddOrUpdateTank(result);

                if (Integration.UseKafka)
                {

                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                    {
                        TankMergedMessage tankMergedMessage = new TankMergedMessage(
                            tankId: result.Id,
                            kind: result.Kind,
                            description: $"Created Merged Tank {result.Name} - Jar version ${result.Version}",
                            jarVersion: result.Version,
                            originTankId: otherTank.Id,
                            createdAt: createdAt,
                            depositIds: result.Deposits.Select(d => d.Id).ToList()
                        );
                        buffer.Send(tankMergedMessage);
                    }
                }

                if (itIsThePresent)
                {
                    CreatedTankEvent createdTankEvent = new CreatedTankEvent(createdAt, result.Id, result.Name);
                    PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);
                }

                TankDiscarded thisTankDiscarded = new TankDiscarded(this);
                Source.AddOrUpdateTank(thisTankDiscarded);
                TankDiscarded tankDiscarded = new TankDiscarded(otherTank);
                Source.AddOrUpdateTank(tankDiscarded);
                return result;
            }

            internal TankReady MergeWith(TankReady tankReady)
            {
                if (tankReady == null) throw new ArgumentException("Cannot merge with null tank.");
                if (tankReady.Deposits.Count() == 0) throw new ArgumentException("Cannot merge with an empty tank.");

                List<Deposit> mergedDeposits = new List<Deposit>(this.Deposits)
                .Concat(tankReady.Deposits)
                .Distinct()
                .ToList();

                this.deposits = mergedDeposits;

                decimal newAmount = 0;
                foreach (var deposit in this.deposits)
                {
                    if (deposit == null) throw new GameEngineException("The Deposit is null.");
                    if (deposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");
                    newAmount += deposit.Amount;
                }

                decimal actualMergeAmount = this.Amount + tankReady.Amount;
                if (newAmount != actualMergeAmount) throw new GameEngineException("The amount of the merged tank is not correct.");

                Amount = newAmount;

                TankDiscarded tankDiscarded = new TankDiscarded(tankReady);
                Source.AddOrUpdateTank(tankDiscarded);
                return this;
            }

            internal void Add(IEnumerable<Deposit> depositSelected)
            {
                if (depositSelected == null || !depositSelected.Any()) throw new ArgumentException("Deposits cannot be null or empty.");
                foreach (var deposit in depositSelected)
                {
                    if (deposit == null) throw new GameEngineException("The Deposit is null.");
                    if (deposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");
                    this.deposits.Add(deposit);
                    Amount += deposit.Amount;
                }
                
            }
        }

        internal class TankRoot : Tank
        {
            internal TankRoot(int id, string name, string description, DateTime createdAt, Jar jar, string kind, List<Deposit> deposits) : base(id, name, description, createdAt, jar, kind, deposits) { }

            internal void Add(IEnumerable<Deposit> depositSelected)
            {
                if (depositSelected == null || !depositSelected.Any()) throw new ArgumentException("Deposits cannot be null or empty.");
                foreach (var deposit in depositSelected)
                {
                    if (deposit == null) throw new GameEngineException("The Deposit is null.");
                    if (deposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");
                    this.deposits.Add(deposit);
                    Amount += deposit.Amount;
                }
            }
        }


        internal class TankLocked : Tank
        {
            internal TankLocked(TankReady tankReady) : base(tankReady.Id, tankReady.Name, tankReady.Description, tankReady.CreatedAt, tankReady.jar, tankReady.Kind, tankReady.deposits) { }
            internal TankLocked(TankRoot tankReady) : base(tankReady.Id, tankReady.Name, tankReady.Description, tankReady.CreatedAt, tankReady.jar, tankReady.Kind, tankReady.deposits) { }

            internal List<Deposit> TakeRemainingDeposits(IEnumerable<int> keepDepositIds)
            {
                if (keepDepositIds == null || !keepDepositIds.Any()) throw new ArgumentException("Deposit IDs cannot be null or empty.");
                List<Deposit> remainingDeposits = new List<Deposit>();
                List<Deposit> keepDeposits = new List<Deposit>();
                foreach (var deposit in deposits)
                {
                    if (!keepDepositIds.Contains(deposit.Id))
                    {
                        remainingDeposits.Add(deposit);
                    }
                    else
                    {
                        keepDeposits.Add(deposit);
                    }
                }

                deposits = keepDeposits;
                return remainingDeposits;
            }

            internal TankReady CreateTank(int newTankId, DateTime createdAt, List<Deposit> remainingDeposits)
            {
                if (remainingDeposits == null || !remainingDeposits.Any()) throw new ArgumentException("Remaining deposits cannot be null or empty.");
                if (newTankId <= 0) throw new GameEngineException("The new tank ID must be greater than zero.");
                if (this.jar == null) throw new GameEngineException("The Jar is null.");
                if (this.Kind != Kind) throw new GameEngineException("The Kind of the tank does not match.");
                
                TankReady tankReady = new TankReady(
                    newTankId,
                    $"{Name} - Partial From Tank ID {Id}",
                    Description,
                    createdAt,
                    this.jar,
                    this.Kind,
                    remainingDeposits
                );
                Source.AddOrUpdateTank(tankReady);
                return tankReady;
            }
        }

        internal class TankDispatched : Tank
        {
            internal TankDispatched(TankLocked tankLocked) : base(tankLocked.Id, tankLocked.Name, tankLocked.Description, tankLocked.CreatedAt, tankLocked.jar, tankLocked.Kind, tankLocked.deposits) { }

        }

        internal class TankDiscarded : Tank
        {
            internal TankDiscarded(TankLocked tankLocked) : base(tankLocked.Id, tankLocked.Name, tankLocked.Description, tankLocked.CreatedAt, tankLocked.jar, tankLocked.Kind, tankLocked.deposits) { }
            internal TankDiscarded(TankReady tankReady) : base(tankReady.Id, tankReady.Name, tankReady.Description, tankReady.CreatedAt, tankReady.jar, tankReady.Kind, tankReady.deposits) { }
            internal TankDiscarded(TankDispatched dispachedTank) : base(dispachedTank.Id, dispachedTank.Name, dispachedTank.Description, dispachedTank.CreatedAt, dispachedTank.jar, dispachedTank.Kind, dispachedTank.deposits) { }
        }
    }
}
