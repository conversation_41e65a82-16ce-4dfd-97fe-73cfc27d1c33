﻿using GamesEngine.Business.Liquidity.Transactions;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal abstract class Tanker : Container
    {
        internal Tanker(string kind, Source source) : base(kind)
        {
            if (source == null) throw new GameEngineException("The Source is null.");
            if (source.Jar == null) throw new GameEngineException("The Jar is null.");
            this.Source = source;
        }

        internal Source Source { get; private set; }
        internal string Type => this.GetType().Name;

        internal int Id { get; private set; }
        internal string Name { get; set; }
        internal string Description { get; set; }
        internal DateTime CreatedAt { get; set; }


        internal abstract IEnumerable<Tank> Tanks { get; }

        internal int TotalTanks => Tanks.Count();

        internal TankerSummary BuildMonthlySummary()
        {
            var realTanks = this.Tanks.Where(t => !(t is TankRoot));
            var rootTank = this.Tanks.OfType<TankRoot>().FirstOrDefault();

            var tankGroups = realTanks
                .GroupBy(tank => tank.CreatedAt.ToString("MM/yyyy"))
                .ToDictionary(g => g.Key, g => g.ToList());

            var depositGroups = new Dictionary<string, List<Deposit>>();
            if (rootTank != null)
            {
                depositGroups = rootTank.Deposits
                    .GroupBy(dep => dep.CreatedAt.ToString("MM/yyyy"))
                    .ToDictionary(g => g.Key, g => g.ToList());
            }

            var allMonthKeys = tankGroups.Keys.Union(depositGroups.Keys).OrderBy(k => k).ToList();
            var monthlySummaries = new List<MonthlyTankSummary>();

            foreach (var key in allMonthKeys)
            {
                var tanksInMonth = tankGroups.ContainsKey(key) ? tankGroups[key] : new List<Tank>();
                var depositsInMonth = depositGroups.ContainsKey(key) ? depositGroups[key] : new List<Deposit>();

                var tankDetails = tanksInMonth.Select(t => new TankDetail(t.Id, t.Name, t.Type, t.Amount, t.CreatedAt)).ToList();
                var depositDetails = depositsInMonth.Select(d => new DepositDetail(d.Id, d.InvoiceId, d.Amount, d.CreatedAt)).ToList();

                var monthTotal = tankDetails.Sum(t => t.Amount) + depositDetails.Sum(d => d.Amount);

                monthlySummaries.Add(new MonthlyTankSummary(key, monthTotal, tankDetails, depositDetails));
            }

            return new TankerSummary(this.Amount, monthlySummaries);
        }

        public class DepositDetail : Objeto
        {
            public int Id { get; }
            public string InvoiceId { get; }
            public decimal Amount { get; }
            public string CreatedAt { get; }

            public DepositDetail(int id, string invoiceId, decimal amount, DateTime createdAt)
            {
                Id = id;
                InvoiceId = invoiceId;
                Amount = amount;
                CreatedAt = createdAt.ToString("MM/dd/yyyy HH:mm:ss");
            }
        }

        public class TankDetail : Objeto
        {
            public int Id { get; }
            public string Name { get; }
            public string Type { get; }
            public decimal Amount { get; }
            public string CreatedAt { get; }

            public TankDetail(int id, string name, string type, decimal amount, DateTime createdAt)
            {
                Id = id;
                Name = name;
                Type = type;
                Amount = amount;
                CreatedAt = createdAt.ToString("MM/dd/yyyy HH:mm:ss");
            }
        }

        public class MonthlyTankSummary : Objeto
        {
            public string Month { get; }
            public decimal MonthTotal { get; }
            public IEnumerable<TankDetail> Tanks { get; }
            public IEnumerable<DepositDetail> RootDeposits { get; }

            public MonthlyTankSummary(string month, decimal monthTotal, IEnumerable<TankDetail> tanks, IEnumerable<DepositDetail> rootDeposits)
            {
                Month = month;
                MonthTotal = monthTotal;
                Tanks = tanks ?? new List<TankDetail>();
                RootDeposits = rootDeposits ?? new List<DepositDetail>();
            }
        }

        public class TankerSummary : Objeto
        {
            public decimal TankerTotalAmount { get; }
            public IEnumerable<MonthlyTankSummary> MonthlySummaries { get; }

            public TankerSummary(decimal tankerTotal, IEnumerable<MonthlyTankSummary> monthlySummaries)
            {
                TankerTotalAmount = tankerTotal;
                MonthlySummaries = monthlySummaries;
            }
        }

        internal class TankerPending : Tanker
        {
            private readonly List<Tank> tanksReady= new List<Tank>();
            private readonly TankRoot rootTank;
            private const string DEFAULT_ROOT_NAME = "Root Tank";
            private const string DEFAULT_ROOT_DESCRIPTION = "Root tank";

            internal TankerPending(int tankerId, string name, string description, DateTime createdAt, string kind, Source source, IEnumerable<TankReady> tanksReady) : base(kind, source)
            {
                rootTank = new TankRoot(source.NextTankId(), DEFAULT_ROOT_NAME, DEFAULT_ROOT_DESCRIPTION, createdAt, source.Jar, kind, new List<Deposit>());
                source.AddOrUpdateTank(rootTank);
                this.tanksReady.Add(rootTank);
                foreach (var tank in tanksReady)
                {
                    this.tanksReady.Add(tank);
                }
                this.Name = name;
                this.Description = description;
                this.Id = tankerId;
                this.CreatedAt = createdAt;
                foreach (var tankReady in this.tanksReady)
                {
                    Amount += tankReady.Amount;
                }
               ;
            }

            internal override IEnumerable<Tank> Tanks =>  tanksReady;

            internal void Add(TankReady tankReady)
            {
                if (tankReady == null) throw new GameEngineException("The TankReady is null.");
                if(tanksReady.Contains(tankReady)) throw new GameEngineException("The TankReady already exists in the TankerPending.");

                tanksReady.Append(tankReady);
                Amount += tankReady.Amount;
            }

            internal void Add(List<TankReady> tanksReady)
            {
                if(tanksReady == null) throw new GameEngineException("The TankReady list is null.");
                if (!tanksReady.Any()) throw new GameEngineException("The TankReady list is empty.");
                
                foreach (var tankReady in tanksReady)
                {
                    if (tankReady == null) throw new GameEngineException("The TankReady is null.");
                    if (this.tanksReady.Contains(tankReady)) throw new GameEngineException("The TankReady already exists in the TankerPending.");
                }

                for (int i = 0; i < tanksReady.Count; i++)
                {
                    this.tanksReady.Add(tanksReady[i]);
                    Amount += tanksReady[i].Amount;
                }
                
            }


            internal void Add(List<Deposit> depositsSelected)
            {
                if(depositsSelected == null) throw new GameEngineException("The Deposit list is null.");
                if (!depositsSelected.Any()) throw new GameEngineException("The Deposit list is empty.");
                if (depositsSelected.Any(d => d == null)) throw new GameEngineException("The Deposit list contains null values.");

                rootTank.Add(depositsSelected);

                for (int i = 0; i < depositsSelected.Count; i++)
                {
                    Amount += depositsSelected[i].Amount;
                }
            }

            internal void AddDepositsByIds(IEnumerable<int> depositIds)
            {
                if (depositIds == null || !depositIds.Any()) return;

                var depositsToAdd = new List<Deposit>();
                foreach (var id in depositIds)
                {
                    var deposit = this.Source.Jar.FindDepositById(id);
                    if (deposit == null) throw new GameEngineException($"Deposit with ID {id} not found in the current Jar.");
                    depositsToAdd.Add(deposit);
                }

                this.Add(depositsToAdd);
            }

            internal TankerSealed Sealed()
            {
                if (tanksReady == null || !tanksReady.Any()) throw new GameEngineException("The TankerPending has no tanks ready.");

                var result = new TankerSealed(this);
                Source.Amount += Amount;
                Source.AddOrUpdateTanker(result);

                return result;
            }

        }


        internal class TankerSealed : Tanker
        {
            private TankerPending tankerPending;
            private readonly List<TankLocked> lockedTanks = new();


            internal TankerSealed(TankerPending tankerPending) : base(tankerPending.Kind, tankerPending.Source)
            {
                this.tankerPending = tankerPending;
                this.Name = tankerPending.Name;
                this.Description = tankerPending.Description;
                this.Id = tankerPending.Id;
                this.Amount = tankerPending.Amount;

                var tanks = tankerPending.Tanks;
                foreach (var tank in tanks)
                {
                    TankLocked tankLocked ;
                    if(tank is TankRoot tankRoot)
                    {
                        tankLocked = new TankLocked(tankRoot);
                    }
                    else if(tank is TankReady tankReady)
                    {
                        tankLocked = new TankLocked(tankReady);
                    }
                    else
                    {
                        throw new GameEngineException($"The Tank type {tank.GetType().Name} is not supported in TankerSealed.");
                    }

                    lockedTanks.Add(tankLocked);
                    Source.AddOrUpdateTank(tankLocked);
                }
            }

            internal override IEnumerable<TankLocked> Tanks => lockedTanks;

            internal IEnumerable<Deposit> Deposits
                {
                get
                {
                    foreach (var tank in Tanks)
                    {
                        foreach (var deposit in tank.Deposits)
                        {
                            yield return deposit;
                        }
                    }
                }
            }

            internal TankerSealed PartialDispatched(int partialTankerId, DateTime dispachedDate, IEnumerable<int> dispatchDeposits)
            {
                if (dispachedDate == DateTime.MinValue) throw new ArgumentNullException(nameof(dispachedDate));
                if (dispatchDeposits == null || !dispatchDeposits.Any()) throw new ArgumentNullException(nameof(dispatchDeposits));

                Dictionary<TankLocked, (List<Deposit> RemainingDeposits, bool StillHasDeposits) > tankDepositsMap = new();
                foreach (var tank in Tanks)
                {
                    List<Deposit> remainingDeposits = tank.TakeRemainingDeposits(dispatchDeposits);
                    if (!remainingDeposits.Any()) continue;

                    bool tankStillHasDeposits = tank.Deposits.Any();
                    tankDepositsMap.Add(tank, (remainingDeposits, tankStillHasDeposits));
                }

                List<TankReady> tankReadies = new List<TankReady>();
                foreach (var tankMap in tankDepositsMap)
                {
                    var loackedTank = tankMap.Key;
                    var remainingDeposits = tankMap.Value.RemainingDeposits;
                    bool stillHasDeposits = tankMap.Value.StillHasDeposits;
                    if (!stillHasDeposits)
                    {
                        lockedTanks.Remove(loackedTank);
                        TankDiscarded tankDiscarded = new TankDiscarded(loackedTank);
                        Source.AddOrUpdateTank(tankDiscarded);
                    }

                    int newTankId = Source.NextTankId();
                    var newTankReady = loackedTank.CreateTank(newTankId, dispachedDate, remainingDeposits);
                    Source.AddOrUpdateTank(newTankReady);
                    tankReadies.Add(newTankReady);
                }
                if (!tankReadies.Any()) throw new GameEngineException("No tanks where found with the provided deposits to dispatch.");

                TankerPending partialPending = new TankerPending(
                    partialTankerId,
                    $"{this.tankerPending.Name} - Partial From Tanker ID {this.Id}",
                    this.tankerPending.Description,
                    dispachedDate,
                    this.tankerPending.Kind,
                    this.Source,
                    tankReadies
                );
                TankerSealed partialTanker = new TankerSealed(this.tankerPending);
                Source.AddOrUpdateTanker(partialTanker);

                var dispatchCurrentTanker = new TankerDispatched(this, dispachedDate);
                Source.AddOrUpdateTanker(dispatchCurrentTanker);

                return partialTanker;
            }

            internal TankerDispatched Dispatched(DateTime dispachedDate)
            {
                if (dispachedDate == DateTime.MinValue) throw new ArgumentNullException(nameof(dispachedDate));
                var result = new TankerDispatched(this, dispachedDate);
                Source.AddOrUpdateTanker(result);
            
                return result;
            }

            internal void Disburden(bool itIsThePresent)
            {
                Source.Liquid.IngressSentinel.AwaitForTanker(itIsThePresent, this);
            }

            internal void Undisburden()
            {
                Source.Liquid.IngressSentinel.UndisburdenTanker(this);
            }

            public class DispatchTankerMessage : LiquidityEventMessage
            {
                internal int TankerId { get; private set; }
                internal string Kind { get; private set; }

                internal DispatchTankerMessage(int tankerId, string kind) : base(LiquidityEventType.TankerDispatched)
                {
                    if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));
                    if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
                    TankerId = tankerId;
                    Kind = kind;
                }

                public DispatchTankerMessage(string message) : base(message)
                {
                }

                protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
                {
                    base.Deserialize(serializedMessage, out fieldOrder);
                    TankerId = int.Parse(serializedMessage[fieldOrder++]);
                    Kind = serializedMessage[fieldOrder++];
                }

                protected override void InternalSerialize()
                {
                    base.InternalSerialize();
                    AddProperty(TankerId).
                    AddProperty(Kind);
                }
            }
        }

        internal class TankerDispatched : Tanker
        {
            private TankerSealed tankerSealed;
            private readonly List<TankDispatched> dispachedTanks = new();

            internal DateTime DispatchedDate { get; private set; }

            internal TankerDispatched(TankerSealed tankerSealed, DateTime dispatchedDate) : base(tankerSealed.Kind, tankerSealed.Source)
            {
                if (tankerSealed == null) throw new ArgumentNullException(nameof(tankerSealed));
                if (dispatchedDate == DateTime.MinValue) throw new ArgumentNullException(nameof(dispatchedDate));
                this.tankerSealed = tankerSealed;

                this.Id = tankerSealed.Id;
                DispatchedDate = dispatchedDate;

                var lockedTanks = tankerSealed.Tanks;
                foreach (var tankLocked in lockedTanks)
                {
                    var tankDispatched = new TankDispatched(tankLocked);
                    Source.AddOrUpdateTank(tankDispatched);
                    dispachedTanks.Append(tankDispatched);
                }
            }

            internal override IEnumerable<TankDispatched> Tanks => dispachedTanks;

            internal TankerArchived Archived()
            {
                var result = new TankerArchived(this);
                Source.AddOrUpdateTanker(result);
                return result;
            }
        }

        internal class TankerArchived : Tanker
        {
            private TankerDispatched tankerDispached;
            private readonly List<TankDiscarded> discardedTanks = new();

            internal TankerArchived(TankerDispatched tankerDispached) : base(tankerDispached.Kind, tankerDispached.Source)
            {
                this.tankerDispached = tankerDispached;

                foreach (var tankDispatched in tankerDispached.Tanks)
                {
                    var tankDiscarded = new TankDiscarded(tankDispatched);
                    Source.AddOrUpdateTank(tankDiscarded);
                    discardedTanks.Append(tankDiscarded);
                }
            }
            internal override IEnumerable<TankDiscarded> Tanks => discardedTanks;

        }
    }
}
