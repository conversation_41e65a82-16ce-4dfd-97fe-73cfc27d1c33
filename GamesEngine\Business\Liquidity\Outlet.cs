﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Domains;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Bottle;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;

namespace GamesEngine.Business.Liquidity
{
    internal class Outlet : Objeto
    {
        private readonly Dictionary<int, Dispenser> _dispensers = new();
        private readonly Dictionary<int, Bottle> _bottles = new();

        public Outlet(Liquid liquid)
        {
            if (liquid == null) throw new ArgumentNullException(nameof(liquid), "Liquid cannot be null.");
            Liquid = liquid;
            DispenserInbox = new DispenserInbox(liquid.Kind, liquid);
            AddOrUpdateDispenser(DispenserInbox);
        }

        internal string Kind => Liquid.Kind;
        internal decimal Amount { get; private set; }
        internal decimal InAmount { get; private set; }

        internal Liquid Liquid { get; private set; }

        internal DispenserInbox DispenserInbox { get; private set; }

        internal IEnumerable<Dispenser> Dispensers => _dispensers.Values;

        internal IEnumerable<Bottle> Bottles => _bottles.Values;

        private int withdrawalConsecutive = 0;
        internal int NextWithdrawalId()
        {
            return withdrawalConsecutive + 1;
        }

        internal bool ExistWithdrawal(int withdrawalId)
        {
            if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));

            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var withdrawal in dispenser.Withdrawals)
                {
                    if (withdrawal.Id == withdrawalId)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        internal bool ExistWithdrawalWithPullPaymentId(string pullPaymentId)
        {
            if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));

            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var withdrawal in dispenser.Withdrawals)
                {
                    if (withdrawal.PullPaymentId == pullPaymentId)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        internal Withdrawal FindWithdrawalFromPullPaymentId(string pullPaymentId)
        {
            if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));

            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var withdrawal in dispenser.Withdrawals)
                {
                    if (withdrawal.PullPaymentId == pullPaymentId)
                    {
                        return withdrawal;
                    }
                }
            }
            throw new GameEngineException($"Withdrawal with pull payment id {pullPaymentId} not found.");
        }

        internal Withdrawal FindWithdrawal(int withdrawalId)
        {
            if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));
            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var withdrawal in dispenser.Withdrawals)
                {
                    if (withdrawal.Id == withdrawalId)
                    {
                        return withdrawal;
                    }
                }
            }
            throw new GameEngineException($"Withdrawal with ID {withdrawalId} not found.");
        }

        private int bottleConsecutive = 0;
        internal int NextBottleId()
        {
            return bottleConsecutive + 1;
        }

        private int dispenserConsecutive = 0;
        internal int NextDispenserId()
        {
            return dispenserConsecutive + 1;
        }

        internal Dispenser FindDispenser(int dispenserId)
        {
            if (dispenserId <= 0) throw new GameEngineException($"Dispenser ID must be greater than zero. Provided ID: {dispenserId}");
            if (_dispensers.TryGetValue(dispenserId, out Dispenser foundDispenser))
            {
                return foundDispenser;
            }
            throw new GameEngineException($"Dispenser with ID {dispenserId} not found.");
        }

        internal bool ExistDispenserFromWithdrawal(Withdrawal withdrawal)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var _withdrawal in dispenser.Withdrawals)
                {
                    if (_withdrawal == withdrawal)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        internal Dispenser FindDispenserFromWithdrawal(Withdrawal withdrawal)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));

            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var _withdrawal in dispenser.Withdrawals)
                {
                    if (_withdrawal == withdrawal)
                    {
                        return dispenser;
                    }
                }
            }
            throw new GameEngineException($"Withdrawal with ID {withdrawal.Id} not found in any dispenser.");
        }

        internal DispenserReady CreateDispenser(bool itIsThePresent, DateTime now, int id, string name, string description, decimal amount, DateTime startDate)
        {
            if (now == DateTime.MinValue) throw new GameEngineException("The CreatedAt date is invalid.");
            if (id <= 0) throw new GameEngineException("The Id is invalid.");
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The Name is null or empty.");
            if (string.IsNullOrWhiteSpace(description)) throw new GameEngineException("The Description is null or empty.");
            if (amount <= 0) throw new GameEngineException("The Amount is invalid.");
            if (startDate == DateTime.MinValue) throw new GameEngineException("The startDate is invalid.");

            //if (amount > Liquid.Source.Amount) throw new GameEngineException("The amount is greater than the available amount.");

            var dispenser = new DispenserReady(id, name, description, this.Liquid, this.Kind, amount, startDate);
            AddOrUpdateDispenser(dispenser);

            if (Integration.UseKafka)
            {

                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    var createdDispenserMessage = new CreatedDispenserMessage(id, this.Kind, amount, now);
                    buffer.Send(createdDispenserMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedDispenserEvent createdDispenserEvent = new CreatedDispenserEvent(now, dispenser.Id, dispenser.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(createdDispenserEvent);
            }
            return dispenser;
        }

        internal void AddOrUpdateDispenser(Dispenser dispenser)
        {
            if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));
            if (_dispensers.TryGetValue(dispenser.Id, out Dispenser foundDispenser))
            {
                _dispensers[dispenser.Id] = dispenser;
            }
            else
            {
                _dispensers.Add(dispenser.Id, dispenser);
                if (!(dispenser is DispenserInbox))
                {
                    dispenserConsecutive = dispenser.Id;
                }
            }
        }

        internal Bottle FindBottle(int bottleId)
        {
            if (bottleId <= 0) throw new ArgumentException("Bottle ID must be greater than zero.", nameof(bottleId));
            if (_bottles.TryGetValue(bottleId, out Bottle foundBottle))
            {
                return foundBottle;
            }
            return null;
        }

        internal void AddOrUpdateBottle(Bottle bottle)
        {
            if (bottle == null) throw new ArgumentNullException(nameof(bottle));
            if (_bottles.TryGetValue(bottle.Id, out Bottle foundBottle))
            {
                _bottles[bottle.Id] = bottle;
            }
            else
            {
                _bottles.Add(bottle.Id, bottle);
                bottleConsecutive = bottle.Id;
            }
        }

        internal BottlePending CreateBottle(bool itIsThePresent, DateTime now, int bottleId, string name, DispenserReady dispenser)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now), "Date cannot be empty.");
            if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));
            if (bottleId <= 0) throw new ArgumentException("Bottle ID must be greater than zero.", nameof(bottleId));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Name cannot be null or empty.", nameof(name));

            var bottle = new BottlePending(bottleId, name, Kind, Liquid, now);
            AddOrUpdateBottle(bottle);

            if (Integration.UseKafka)
            {
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    CreatedBottleMessage createdDispenserMessage = new CreatedBottleMessage(bottleId, name, Kind, now);
                    buffer.Send(createdDispenserMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedBottleEvent createdBottleEvent = new CreatedBottleEvent(now, bottle.Id, bottle.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(createdBottleEvent);
            }

            return bottle;
        }

        internal bool ExistDispenser(int dispenserId)
        {
            if (dispenserId <= 0) throw new ArgumentException("Dispenser ID must be greater than zero.", nameof(dispenserId));
            return _dispensers.ContainsKey(dispenserId);
        }

        internal Withdrawal CreateWithdrawal(bool itIsThePresent, DateTime now, int withdrawalId, string pullPaymentId, int authorization, decimal amount, string destionation, string atAddress, Domain domain, int storeId, string externalReference)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));
            if (authorization <= 0) throw new ArgumentNullException(nameof(authorization));
            if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));
            if (amount <= 0) throw new ArgumentNullException(nameof(amount));
            if (string.IsNullOrWhiteSpace(destionation)) throw new ArgumentNullException(nameof(destionation));
            if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (storeId == 0) throw new ArgumentNullException(nameof(storeId));
            if (string.IsNullOrWhiteSpace(externalReference)) throw new ArgumentNullException(nameof(externalReference));

            var withdrawal = DispenserInbox.CreateWithdrawal(now, withdrawalId, pullPaymentId, authorization, amount, destionation, atAddress, domain, storeId, externalReference);
            withdrawalConsecutive = withdrawalId;

            if (itIsThePresent)
            {
                if (Integration.UseKafka)
                {
                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForContainerEvents))
                    {
                        var createdWithdrawalMessage = new CreatedWithdrawalMessage(this.Kind, withdrawal.Id, withdrawal.Amount, withdrawal.CreatedDate, withdrawal.Destination, withdrawal.AtAddress, withdrawal.ExternalReference, withdrawal.Reference, domain.Id);
                        buffer.Send(createdWithdrawalMessage);
                    }
                }
                //CreatedWithdrawalEvent createdWithdrawalEvent = new CreatedWithdrawalEvent(now, withdrawal.Id, withdrawal.Amount, withdrawal.Destination);
                //PlatformMonitor.GetInstance().WhenNewEvent(createdWithdrawalEvent);
            }

            return withdrawal;
        }

        internal void CancelWithdrawal(bool itIsThePresent, DateTime now, int withdrawalId)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now), "Date cannot be empty.");
            if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));

            DispenserInbox.CancelWithdrawal(now, withdrawalId);
            withdrawalConsecutive = withdrawalId;
        }

        internal IEnumerable<Dispenser> FindDispensersBy(string nameFilter)
        {
            var result = _dispensers.Values.Where(d => !(d is Dispenser.DispenserInbox));

            if (!string.IsNullOrWhiteSpace(nameFilter))
            {
                result = result.Where(d => d.Name.IndexOf(nameFilter, StringComparison.OrdinalIgnoreCase) >= 0);
            }

            return result.ToList();
        }
    }
}
