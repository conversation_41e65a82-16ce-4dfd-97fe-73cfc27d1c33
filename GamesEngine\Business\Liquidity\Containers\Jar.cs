﻿using Connectors.town.connectors.drivers.consignment;
using Elastic.Clients.Elasticsearch;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Domains;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using Deposit = GamesEngine.Business.Liquidity.Transactions.Deposit;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal class Jar : Container
    {
        private readonly Dictionary<int, Deposit> wholeDeposits = new Dictionary<int, Deposit>();
        private readonly Dictionary<int, Deposit> completedDeposits = new Dictionary<int, Deposit>();

        internal Jar(Source source, string kind, int vesion) : base(kind)
        {
            if (source == null) throw new ArgumentNullException(nameof(source));
            Source = source;
            Version = vesion;
        }

        internal Jar(Source source, string kind, int vesion, LegacyJar legacyJar, IEnumerable<Deposit> deposits) : base(kind) {

            if (source == null) throw new ArgumentNullException(nameof(source));
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
            if (vesion <= 0) throw new ArgumentException("Version cannot be less than or equal to 0.", nameof(vesion));
            if(deposits == null) throw new ArgumentException("Deposits cannot be null or empty.", nameof(deposits));

            Source = source;
            Version = vesion;
            PreviousLegacyJar = legacyJar;
            if (deposits.Count() != 0)
            {
                foreach (Deposit deposit in deposits)
                {
                    if (deposit == null) throw new GameEngineException("The deposit is null.");
                    if (deposit.Amount < 0) throw new GameEngineException("The deposit amount is negative.");
                    if (this.wholeDeposits.ContainsKey(deposit.Id)) throw new GameEngineException($"The deposit with id {deposit.Id} already exists in the jar.");
                    this.wholeDeposits.Add(deposit.Id, deposit);
                    Amount += deposit.Amount;

                    if (deposit.IsConfirmed)
                    {
                        completedDeposits.Add(deposit.Id,deposit);
                    }
                }
            }
        }

        internal LegacyJar PreviousLegacyJar { get; private set; }
        internal Source Source { get; private set; }
        internal int Version { get; private set; }
        internal string Address => $"JAR_{Kind}_V{Version}";
        internal decimal MinimumAmount { get; private set; }

        internal IEnumerable<Deposit> Deposits => wholeDeposits.Values;

        internal decimal AvailableAmount
        {
            get
            {
                decimal result = 0;
                foreach (Deposit deposit in completedDeposits.Values)
                {
                    result += deposit.Amount;
                }
                return result;
            }
        }

        internal Deposit CreateDraftDeposit(bool itIsThePresent, DateTime createdAt, string address, int depositId, string invoiceId, int authorizationId, int externalReference, string destination, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int totalConfirmations, int storeId, Domain domain)
        {
            return CreateDraftDeposit(itIsThePresent, createdAt, address, depositId, invoiceId, authorizationId, string.Empty, externalReference, destination, exchangeAmount, exchangeRate, confirmedCurrency, confirmedAmount, totalConfirmations, storeId, domain);
        }

        internal Deposit CreateDraftDeposit(bool itIsThePresent, DateTime createdAt, string address, int depositId, string invoiceId, int authorizationId, string externalAtAddress, int externalReference, string destination, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int totalConfirmations, int storeId, Domain domain)
        {
            if (Version != Source.CurrentVesion) throw new GameEngineException($"This jar with version {Version} is not the latest version with {Source.CurrentVesion}. Please ensure you are using the most up-to-date jar version.");
            if (domain == null) throw new GameEngineException("The domain is null or empty.");
            if (storeId <= 0) throw new GameEngineException("The store id is zero or negative.");

            if (depositId <= 0) throw new GameEngineException("The deposit id is zero or negative.");
            if (string.IsNullOrWhiteSpace(destination)) throw new GameEngineException("The invoice id is null or empty.");
            if (createdAt == DateTime.MinValue) throw new GameEngineException("The createdAt date is invalid or empty.");

            foreach (Deposit deposit in wholeDeposits.Values)
            {
                if (deposit.Address == address) throw new GameEngineException($"The address: {address} is already associated with an existing deposit.");
            }

            if (string.IsNullOrWhiteSpace(invoiceId)) throw new GameEngineException("The invoice id is null or empty.");
            if (string.IsNullOrWhiteSpace(destination)) throw new GameEngineException("The destination is null or empty.");
            if (exchangeAmount <= 0) throw new GameEngineException("The exchange amount is zero or negative.");
            if (exchangeRate <= 0) throw new GameEngineException("The rate is zero or negative.");

            if (authorizationId <= 0) throw new GameEngineException("The authorization id is zero or negative.");
            if (externalReference <= 0) throw new GameEngineException("The external reference is zero or negative.");

            if (string.IsNullOrWhiteSpace(confirmedCurrency)) throw new GameEngineException("The currency is null or empty.");
            if (confirmedAmount <= 0) throw new GameEngineException("The amount is zero or negative.");

            if (totalConfirmations < 0) throw new GameEngineException("The total confirmations cannot be negative.");

            if (wholeDeposits.ContainsKey(depositId)) throw new InvalidOperationException($"Deposit Id {depositId} already exists in the jar.");
            if (completedDeposits.ContainsKey(depositId)) throw new GameEngineException($"The deposit with id {depositId} is already confirmed or completed.");

            Deposit depositResult = new Deposit(
                depositId,
                address,
                createdAt, 
                this, 
                invoiceId, 
                authorizationId,
                externalAtAddress,
                externalReference,
                destination,
                exchangeAmount,
                exchangeRate, 
                confirmedCurrency, 
                confirmedAmount,
                storeId,
                domain
            );


            Source.DepositConsecutive = depositResult.Id;
            wholeDeposits.Add(depositId, depositResult);
            Amount += depositResult.Amount;

            Source.Liquid.IngressSentinel.AwaitForDeposit(depositResult, totalConfirmations);

            if (Integration.UseKafka)
            {
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForContainerEvents))
                {
                    DraftDepositMessage deposit = new DraftDepositMessage(
                        depositResult.Id,
                        Kind,
                        depositResult.Address,
                        destination,
                        confirmedAmount,
                        createdAt,
                        Version,
                        Address,
                        depositResult.Domain.Id
                    );
                    buffer.Send(deposit);
                }
            }
            if (itIsThePresent)
            {
                DraftDepositEvent draftDepositConfirmedEvent = new DraftDepositEvent(createdAt, depositResult.Id, depositResult.Amount);
                PlatformMonitor.GetInstance().WhenNewEvent(draftDepositConfirmedEvent);
            }

            return depositResult;
        }

        internal Deposit ConfirmDeposit(bool itIsThePresent, DateTime confirmDate, Deposit draftConfirmDeposit)
        {
            if (Version != Source.CurrentVesion) throw new GameEngineException($"This jar with version {Version} is not the latest version with {Source.CurrentVesion}. Please ensure you are using the most up-to-date jar version.");
            if (draftConfirmDeposit == null) throw new GameEngineException("Draft confirm deposit is null.");
            if (confirmDate == DateTime.MinValue) throw new GameEngineException("The createdAt date is invalid or empty.");

            if (string.IsNullOrEmpty(draftConfirmDeposit.InvoiceId)) throw new GameEngineException("The deposit transaction id is null or empty.");
            if (draftConfirmDeposit.Amount <= 0) throw new GameEngineException("The deposit amount is zero or negative.");

            if (!wholeDeposits.ContainsKey(draftConfirmDeposit.Id)) throw new InvalidOperationException("Deposit Id does not exist.");
            if (completedDeposits.ContainsKey(draftConfirmDeposit.Id)) throw new GameEngineException($"The deposit with id {draftConfirmDeposit.Id} is already confirmed or completed.");

            draftConfirmDeposit.Confirm(confirmDate);
            completedDeposits.Add(draftConfirmDeposit.Id, draftConfirmDeposit);
            //Rubicon: No es necesario notificar a Kafka y SignalR porque ya se notifico en el sentinel
            return draftConfirmDeposit;
        }

        internal Deposit CancelDeposit(bool itIsThePresent, DateTime createdAt, Deposit draftConfirmDeposit)
        {
            if (Version != Source.CurrentVesion) throw new GameEngineException($"This jar with version {Version} is not the latest version with {Source.CurrentVesion}. Please ensure you are using the most up-to-date jar version.");
            if (draftConfirmDeposit == null) throw new GameEngineException("Draft confirm deposit is null.");
            if (createdAt == DateTime.MinValue) throw new GameEngineException("The createdAt date is invalid or empty.");
            if (!wholeDeposits.ContainsKey(draftConfirmDeposit.Id)) throw new InvalidOperationException("Deposit Id does not exist.");
            
            draftConfirmDeposit.Cancel(createdAt);
            Amount -= draftConfirmDeposit.Amount;
            Source.Liquid.IngressSentinel.StopAndDetachInboundTask(draftConfirmDeposit);
            
            return draftConfirmDeposit;
        }

        internal Tank FundTank(decimal amountForTank)
        {
            throw new NotImplementedException();
            if (amountForTank < MinimumAmount) throw new GameEngineException($"The request amount {amountForTank} for the tank is lower than the minimum amount {MinimumAmount}");
            //Rubicon: Todo aqui se deben extraer los depositos de cercanos al monto para crear y devolver un Tank
        }

        internal Deposit FindDepositById(int id)
        {
            if (id <= 0) throw new GameEngineException("The id is invalid or empty.");
            return wholeDeposits[id];
        }


        internal IEnumerable<Deposit> PendingDeposits()
        {
            List<Deposit> pendingDeposits = new List<Deposit>();
            foreach (Deposit deposit in wholeDeposits.Values)
            {
                if (!completedDeposits.ContainsKey(deposit.Id))
                {
                    pendingDeposits.Add(deposit);
                }
            }
            return pendingDeposits;
        }

        internal IEnumerable<Deposit> PendingDeposits(string externalAtAddress)
        {
            if (string.IsNullOrWhiteSpace(externalAtAddress)) throw new GameEngineException("The deposit transaction id is null or empty.");

            List<Deposit> pendingDeposits = new List<Deposit>();
            foreach (Deposit deposit in wholeDeposits.Values)
            {
                if (!completedDeposits.ContainsKey(deposit.Id) && deposit.ExternalAtAddress == externalAtAddress)
                {
                    pendingDeposits.Add(deposit);
                }
            }
            return pendingDeposits;
        }

        internal IEnumerable<Deposit> ConfirmedDeposits()
        {
            return completedDeposits.Values;
        }


        internal decimal EstimateNextTankAmount()
        {
            throw new NotImplementedException();
            //Rubicon: Todo, revisar hasta cuando se considera un deposito como reciente. retronar
            
            //var confirmed = _deposits.Where(d => d.Status == DepositStatus.Confirmed).ToList();
            //return confirmed.Any() ? confirmed.Average(d => d.Amount) : 0;
        }


        internal TankReady CreateTank(bool itIsThePresent, DateTime now, int id, string name, string description, int vesion, IEnumerable<int> selectedDeposits)
        {
            if (id <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");
            if (selectedDeposits.Any(deposit => deposit <= 0)) throw new GameEngineException("One or more deposits are null.");

            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The tank name cannot be null or empty.");

            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");

            var confirmedDepositList = ConfirmedDeposits();
            List<Deposit> selectedDepositsList = new List<Deposit>();

            foreach (int depositId in selectedDeposits)
            {
                Deposit confirmedDeposit = confirmedDepositList.FirstOrDefault(x => x.Id == depositId);
                if (confirmedDeposit == null) throw new GameEngineException($"The deposit with id {depositId} does not exist.");
                selectedDepositsList.Add(confirmedDeposit);
            }
            if(selectedDepositsList.Count == 0) throw new GameEngineException("No deposits where found in selected.");

            return CreateTank(itIsThePresent, now, id, name, description, vesion, selectedDepositsList);
        }

        internal TankReady CreateTank(bool itIsThePresent, DateTime now, int id, string name, string description, int vesion, IEnumerable<Deposit> selectedDeposits)
        {
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");

            if (selectedDeposits.Any(deposit => deposit == null)) throw new GameEngineException("One or more deposits are null.");

            if (id <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The tank name cannot be null or empty.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");

            PreviousLegacyJar = new LegacyJar(this);

            TankReady result = new TankReady(id, name,description, now, this, Kind, selectedDeposits.ToList());

            List<Deposit> delegatedDeposits = new List<Deposit>();
            foreach (Deposit deposit in Deposits)
            {
                bool delegateDeposit = !selectedDeposits.Any(x => x.Id == deposit.Id);
                if (delegateDeposit)
                {
                    delegatedDeposits.Add(deposit);
                }
            }
            Source.DelegateJar(itIsThePresent, now, vesion, PreviousLegacyJar, delegatedDeposits);
            foreach (var deposit in selectedDeposits)
            {
                Amount -= deposit.Amount;
            }

            if (Integration.UseKafka)
            {
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    CreatedTankMessage createdTankMessage = new CreatedTankMessage(
                        tankId: result.Id,
                        kind: Kind,
                        description: $"Created Tank {result.Name} - Jar version ${result.Version}",
                        jarVersion: result.Version,
                        createdAt: now,
                        depositIds: selectedDeposits.Select(x => x.Id).ToList()
                    );
                    buffer.Send(createdTankMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedTankEvent CreatedTankEvent = new CreatedTankEvent(now, result.Id, result.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(CreatedTankEvent);
            }
            Source.AddOrUpdateTank(result);
            return result;
        }

        internal TankReady CreateTank(bool itIsThePresent, DateTime now, int id, string name,string description, int vesion)
        {
            if (id <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (Source.ExistTank(id)) throw new GameEngineException("The tank id is already associated with an existing tank.");
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The tank name cannot be null or empty.");
            if(now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");


            IEnumerable<Deposit> cofirmedDeposits = ConfirmedDeposits();
            if (cofirmedDeposits.Count() == 0) throw new GameEngineException("Cannot create a tank without confirmed deposits.");

            PreviousLegacyJar = new LegacyJar(this);
            TankReady result = new TankReady(id, name, description, now, this, Kind, cofirmedDeposits.ToList());

            List<Deposit> delegatedDeposits = new List<Deposit>();
            foreach (Deposit deposit in Deposits)
            {
                bool delegateDeposit = !cofirmedDeposits.Any(x => x.Id == deposit.Id);
                if (delegateDeposit)
                {
                    delegatedDeposits.Add(deposit);
                }
            }
            Source.DelegateJar(itIsThePresent, now, vesion, PreviousLegacyJar, delegatedDeposits);            
            foreach(var deposit in cofirmedDeposits) Amount -= deposit.Amount;

            if (Integration.UseKafka)
            {
                
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    CreatedTankMessage createdTankMessage = new CreatedTankMessage(
                        tankId: result.Id,
                        kind: Kind,
                        description: $"Created Tank {result.Name} - Jar version ${result.Version}",
                        jarVersion: result.Version,
                        createdAt: now,
                        depositIds: cofirmedDeposits.Select(x => x.Id).ToList()
                    );
                    buffer.Send(createdTankMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedTankEvent CreatedTankEvent = new CreatedTankEvent(now, result.Id, result.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(CreatedTankEvent);
            }

            Source.AddOrUpdateTank(result);
            return result;
        }


        internal void MoveToTank(bool itIsThePresent, DateTime now, int vesion, int tankId, IEnumerable<Deposit> selectedDeposits)
        {
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");

            if (selectedDeposits.Any(deposit => deposit == null)) throw new GameEngineException("One or more deposits are null.");

            if (tankId <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");
            if(!Source.ExistTank(tankId)) throw new GameEngineException($"The tank with id {tankId} does not exist.");
            var tank = Source.FindTank(tankId);
            if (!(tank is TankReady tankReady)) throw new GameEngineException($"The tank with id {tankId} is not a TankReady. It is a {tank.GetType().Name}.");

            List<Deposit> delegatedDeposits = new List<Deposit>();
            foreach (Deposit deposit in Deposits)
            {
                bool delegateDeposit = !selectedDeposits.Any(x => x.Id == deposit.Id);
                if (delegateDeposit)
                {
                    delegatedDeposits.Add(deposit);
                }
            }

            Source.DelegateJar(itIsThePresent, now, vesion, PreviousLegacyJar, delegatedDeposits);
            foreach (var deposit in selectedDeposits)
            {
                Amount -= deposit.Amount;
            }
            
            tankReady.Add(selectedDeposits);

            if (Integration.UseKafka)
            {

                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    MoveLiquidToTankMessage createdTankMessage = new MoveLiquidToTankMessage(
                        tankId: tankReady.Id,
                        kind: Kind,
                        description: $"Move deposits to Tank {tankReady.Name} - Jar version ${tank.Version}",
                        jarVersion: tank.Version,
                        createdAt: now,
                        depositIds: selectedDeposits.Select(x => x.Id).ToList()
                    );
                    buffer.Send(createdTankMessage);
                }
            }

            if (itIsThePresent)
            {
                MoveLiquidToTankEvent CreatedTankEvent = new MoveLiquidToTankEvent(now, tankReady.Id, tankReady.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(CreatedTankEvent);
            }
        }

        internal void MoveToTank(bool itIsThePresent, DateTime now, int vesion, int tankId, IEnumerable<int> selectedDeposits)
        {
            if (tankId <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");
            if (selectedDeposits.Any(deposit => deposit <= 0)) throw new GameEngineException("One or more deposits are null.");
            if (tankId <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");

            var confirmedDepositList = ConfirmedDeposits();
            List<Deposit> selectedDepositsList = new List<Deposit>();

            foreach (int depositId in selectedDeposits)
            {
                Deposit confirmedDeposit = confirmedDepositList.FirstOrDefault(x => x.Id == depositId);
                if (confirmedDeposit == null) throw new GameEngineException($"The deposit with id {depositId} does not exist.");
                selectedDepositsList.Add(confirmedDeposit);
            }
            if (selectedDepositsList.Count == 0) throw new GameEngineException("No deposits where found in selected.");
            MoveToTank(itIsThePresent, now, vesion, tankId, selectedDepositsList);
        }

        internal void MoveToTanker(bool itIsThePresent, DateTime now, int vesion, int tankerId, IEnumerable<int> selectedDeposits)
        {
            if (tankerId <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");
            if (selectedDeposits.Any(deposit => deposit <= 0)) throw new GameEngineException("One or more deposits are null.");
            if (tankerId <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (!Source.ExistTank(tankerId)) throw new GameEngineException($"The tank with id {tankerId} does not exist.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");
            var tanker = Source.FindTanker(tankerId);
            if (!(tanker is TankerPending tankerPending)) throw new GameEngineException($"The tanker with id {tankerId} is not a TankerPending. It is a {tanker.GetType().Name}.");

            var confirmedDepositList = ConfirmedDeposits();
            List<Deposit> selectedDepositsList = new List<Deposit>();

            foreach (int depositId in selectedDeposits)
            {
                Deposit confirmedDeposit = confirmedDepositList.FirstOrDefault(x => x.Id == depositId);
                if (confirmedDeposit == null) throw new GameEngineException($"The deposit with id {depositId} does not exist.");
                selectedDepositsList.Add(confirmedDeposit);
            }
            if (selectedDepositsList.Count == 0) throw new GameEngineException("No deposits where found in selected.");


            List<Deposit> delegatedDeposits = new List<Deposit>();
            foreach (Deposit deposit in Deposits)
            {
                bool delegateDeposit = !selectedDepositsList.Any(x => x.Id == deposit.Id);
                if (delegateDeposit)
                {
                    delegatedDeposits.Add(deposit);
                }
            }

            Source.DelegateJar(itIsThePresent, now, vesion, PreviousLegacyJar, delegatedDeposits);
            foreach (var deposit in selectedDepositsList)
            {
                Amount -= deposit.Amount;
            }

            tankerPending.Add(selectedDepositsList);
        }


        public Tank OptimizedDeposit()
        {
            //Rubicon: Todo, Calcula es los mejores depósitos candidatos para ser movidos  a un Tank. Los Depósitos cumplen en monto
            throw new NotImplementedException();
        }

        public bool ExistDeposit(int depositId)
        {
            if (depositId <= 0) throw new GameEngineException("The deposit id is invalid or empty.");
            return wholeDeposits.ContainsKey(depositId);
        }        
    }
}
