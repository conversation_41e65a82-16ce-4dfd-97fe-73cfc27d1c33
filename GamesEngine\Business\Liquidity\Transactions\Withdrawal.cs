﻿using GamesEngine.Domains;
using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Business.Liquidity.Transactions
{
    internal class Withdrawal : Objeto
    {
        internal int Id { get; private set; }

        internal decimal Amount { get; private set; }
        
        internal DateTime CreatedDate { get; private set; }
        
        internal int Reference { get; private set; }

        internal string ExternalReference { get; private set; }

        internal string Destination { get; private set; }

        internal string AtAddress { get; private set; }

        internal string PullPaymentId { get; private set; }

        internal int Authorization { get; private set; }
        internal int StoreId { get; private set; }
        internal Domain Domain { get; private set; }
        internal string Notes { get; private set; } = string.Empty;
        internal string SetNotes(string notes)
        {
            if (string.IsNullOrWhiteSpace(notes)) throw new ArgumentNullException(nameof(notes));
            Notes = notes;
            return Notes;
        }

        public Withdrawal(int id, string pullPaymentId, int authorization, decimal amount, DateTime createdDate, string destination, int reference, string atAddress, Domain domain, int storeId, string externalReference)
        {
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));
            if (authorization <= 0) throw new ArgumentNullException(nameof(authorization));
            if (amount <= 0) throw new ArgumentNullException(nameof(amount));
            if (createdDate == DateTime.MinValue) throw new ArgumentNullException(nameof(createdDate));
            if (string.IsNullOrWhiteSpace(destination)) throw new ArgumentNullException(nameof(destination));
            if (reference <= 0) throw new ArgumentNullException(nameof(reference));
            if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (storeId == 0) throw new ArgumentNullException(nameof(storeId));
            if (string.IsNullOrWhiteSpace(externalReference)) throw new ArgumentNullException(nameof(externalReference));

            Id = id;
            PullPaymentId = pullPaymentId;
            Authorization = authorization;
            Amount = amount;
            CreatedDate = createdDate;
            Destination = destination;
            Reference = reference;
            AtAddress = atAddress;
            Domain = domain;
            StoreId = storeId;
            ExternalReference = externalReference;
        }
    }
}
